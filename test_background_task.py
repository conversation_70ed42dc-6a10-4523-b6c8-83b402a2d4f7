#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 FastAPI BackgroundTasks 的简单示例
"""

from fastapi import FastAPI, BackgroundTasks
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

def simple_background_task(name: str, delay: int = 5):
    """简单的后台任务"""
    logger.info(f"后台任务开始执行 - 名称: {name}, 延迟: {delay}秒")
    time.sleep(delay)
    logger.info(f"后台任务执行完成 - 名称: {name}")

@app.get("/test-background")
async def test_background(background_tasks: BackgroundTasks, name: str = "test"):
    """测试后台任务的接口"""
    logger.info(f"收到请求，准备添加后台任务 - 名称: {name}")
    
    # 添加后台任务
    background_tasks.add_task(simple_background_task, name, 3)
    
    logger.info(f"后台任务已添加 - 名称: {name}")
    
    return {"message": f"后台任务已启动 - 名称: {name}"}

if __name__ == "__main__":
    import uvicorn
    logger.info("启动测试服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8889)
