#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试异步任务执行的脚本
"""

import sys
import time
from datetime import datetime
sys.path.append('/Users/<USER>/ai-service')

from service.code_review import run_format_task
from common.utils.logger_util import init_logger, get_logger

def test_async_task():
    """测试异步任务执行"""
    logger = init_logger("test_async", "DEBUG")
    logger.info("开始测试异步任务执行")
    
    # 模拟LLM输出
    mock_llm_output = """
    这是一个模拟的代码审查结果：
    
    1. 发现了一些潜在的问题
    2. 代码质量总体良好
    3. 建议进行一些优化
    """
    
    try:
        logger.info("调用run_format_task函数")
        run_format_task(
            project_id="2609",
            mr_iid="995", 
            llm_output=mock_llm_output,
            record_id=1
        )
        logger.info("异步任务调用完成")
        
    except Exception as e:
        logger.error(f"异步任务执行失败: {e}")
        import traceback
        logger.error(f"异常堆栈:\n{traceback.format_exc()}")

if __name__ == "__main__":
    test_async_task()
