#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超时控制的有效性
"""

import sys
import time
import threading
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
sys.path.append('/Users/<USER>/ai-service')


class MockLLMClient:
    """模拟LLM客户端，用于测试超时控制"""
    
    def __init__(self, response_delay: float = 0):
        """
        初始化模拟客户端
        
        Args:
            response_delay: 模拟响应延迟（秒）
        """
        self.response_delay = response_delay
    
    def chat(self, messages):
        """模拟聊天调用"""
        print(f"模拟LLM调用开始，将延迟 {self.response_delay} 秒...")
        time.sleep(self.response_delay)
        print("模拟LLM调用完成")
        return "模拟响应内容"


def test_blocking_timeout_problem():
    """演示阻塞调用的超时问题"""
    print("=== 演示阻塞调用的超时问题 ===")
    
    client = MockLLMClient(response_delay=5)  # 模拟5秒延迟
    timeout_seconds = 2
    
    print(f"设置超时时间: {timeout_seconds} 秒")
    print(f"模拟调用延迟: {client.response_delay} 秒")
    
    # 错误的超时控制方式（事后检查）
    print("\n--- 错误的超时控制方式 ---")
    start_time = time.time()
    
    try:
        response = client.chat([])  # 这里会阻塞5秒
        duration = time.time() - start_time
        
        # 事后检查超时（无效）
        if duration > timeout_seconds:
            raise TimeoutError(f"调用超时: {duration:.3f}秒 > {timeout_seconds}秒")
        
        print(f"调用成功，耗时: {duration:.3f}秒")
        
    except TimeoutError as e:
        print(f"检测到超时: {e}")
        print("但是注意：我们已经等待了5秒才检测到超时！")
    
    total_duration = time.time() - start_time
    print(f"总耗时: {total_duration:.3f}秒")
    
    if total_duration > timeout_seconds:
        print("❌ 超时控制无效：实际等待时间超过了设定的超时时间")
        return False
    else:
        print("✅ 超时控制有效")
        return True


def test_thread_pool_timeout():
    """测试线程池的真正超时控制"""
    print("\n=== 测试线程池的真正超时控制 ===")
    
    client = MockLLMClient(response_delay=5)  # 模拟5秒延迟
    timeout_seconds = 2
    
    print(f"设置超时时间: {timeout_seconds} 秒")
    print(f"模拟调用延迟: {client.response_delay} 秒")
    
    def _make_llm_call():
        """在单独线程中执行LLM调用"""
        return client.chat([])
    
    start_time = time.time()
    
    try:
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(_make_llm_call)
            
            # 真正的超时控制
            response = future.result(timeout=timeout_seconds)
            duration = time.time() - start_time
            print(f"调用成功，耗时: {duration:.3f}秒")
            return False  # 不应该成功
            
    except FutureTimeoutError:
        duration = time.time() - start_time
        print(f"✅ 真正的超时控制生效，耗时: {duration:.3f}秒")
        print("注意：后台线程可能仍在执行，但我们不再等待")
        
        if duration <= timeout_seconds + 0.1:  # 允许0.1秒误差
            print("✅ 超时控制有效：在设定时间内返回")
            return True
        else:
            print("❌ 超时控制无效：超过了设定时间")
            return False
    
    except Exception as e:
        print(f"❌ 意外异常: {e}")
        return False


def test_timeout_with_different_delays():
    """测试不同延迟下的超时控制"""
    print("\n=== 测试不同延迟下的超时控制 ===")
    
    test_cases = [
        (1, 2),   # 延迟1秒，超时2秒 - 应该成功
        (3, 2),   # 延迟3秒，超时2秒 - 应该超时
        (0.5, 1), # 延迟0.5秒，超时1秒 - 应该成功
        (5, 1),   # 延迟5秒，超时1秒 - 应该超时
    ]
    
    results = []
    
    for delay, timeout in test_cases:
        print(f"\n--- 测试：延迟{delay}秒，超时{timeout}秒 ---")
        
        client = MockLLMClient(response_delay=delay)
        
        def _make_llm_call():
            return client.chat([])
        
        start_time = time.time()
        
        try:
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(_make_llm_call)
                response = future.result(timeout=timeout)
                
                duration = time.time() - start_time
                print(f"✅ 调用成功，耗时: {duration:.3f}秒")
                
                expected_success = delay <= timeout
                actual_success = True
                results.append(expected_success == actual_success)
                
        except FutureTimeoutError:
            duration = time.time() - start_time
            print(f"⏰ 调用超时，耗时: {duration:.3f}秒")
            
            expected_success = delay <= timeout
            actual_success = False
            results.append(expected_success == actual_success)
        
        except Exception as e:
            print(f"❌ 意外异常: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n测试成功率: {success_rate:.1f}% ({sum(results)}/{len(results)})")
    
    return success_rate == 100.0


def test_concurrent_timeout_calls():
    """测试并发调用的超时控制"""
    print("\n=== 测试并发调用的超时控制 ===")
    
    def make_call_with_timeout(delay, timeout, call_id):
        """执行带超时的调用"""
        client = MockLLMClient(response_delay=delay)
        
        def _make_llm_call():
            return client.chat([])
        
        start_time = time.time()
        
        try:
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(_make_llm_call)
                response = future.result(timeout=timeout)
                
                duration = time.time() - start_time
                print(f"调用{call_id}: 成功，耗时 {duration:.3f}秒")
                return True
                
        except FutureTimeoutError:
            duration = time.time() - start_time
            print(f"调用{call_id}: 超时，耗时 {duration:.3f}秒")
            return False
        
        except Exception as e:
            print(f"调用{call_id}: 异常 {e}")
            return False
    
    # 并发执行多个调用
    calls = [
        (1, 2, "A"),   # 应该成功
        (3, 2, "B"),   # 应该超时
        (0.5, 1, "C"), # 应该成功
        (4, 2, "D"),   # 应该超时
    ]
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [
            executor.submit(make_call_with_timeout, delay, timeout, call_id)
            for delay, timeout, call_id in calls
        ]
        
        results = [future.result() for future in futures]
    
    total_duration = time.time() - start_time
    print(f"\n并发调用总耗时: {total_duration:.3f}秒")
    print(f"成功调用数: {sum(results)}/{len(results)}")
    
    # 验证并发调用没有相互阻塞
    if total_duration < 5:  # 如果是串行执行，总时间会超过8秒
        print("✅ 并发超时控制正常工作")
        return True
    else:
        print("❌ 并发超时控制可能存在问题")
        return False


def main():
    """主测试函数"""
    print("开始测试超时控制的有效性...")
    
    test_results = []
    
    # 1. 演示阻塞调用的问题
    result = test_blocking_timeout_problem()
    test_results.append(("阻塞调用问题演示", not result))  # 期望失败
    
    # 2. 测试线程池超时控制
    result = test_thread_pool_timeout()
    test_results.append(("线程池超时控制", result))
    
    # 3. 测试不同延迟
    result = test_timeout_with_different_delays()
    test_results.append(("不同延迟测试", result))
    
    # 4. 测试并发调用
    result = test_concurrent_timeout_calls()
    test_results.append(("并发调用测试", result))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed >= 3:  # 允许第一个测试失败（演示问题）
        print("🎉 超时控制机制验证通过！")
        print("\n关键结论:")
        print("1. ❌ 阻塞调用后的时间检查无效")
        print("2. ✅ 线程池的 future.result(timeout) 有效")
        print("3. ✅ 可以在设定时间内中断等待")
        print("4. ✅ 支持并发调用的独立超时控制")
    else:
        print("⚠️  超时控制机制需要进一步检查")


if __name__ == "__main__":
    main()
