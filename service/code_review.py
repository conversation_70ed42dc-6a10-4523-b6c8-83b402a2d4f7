# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森 (重构: 和森)
# @Email  : <EMAIL>
# @File   : code_review.py
# @Project: ai-service
# @Desc   : 代码审查主业务逻辑，重构后专注于业务流程编排

import sys
sys.path.append('/Users/<USER>/ai-service')  # 添加这一行在导入common模块之前

from datetime import datetime
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor

# 导入重构后的模块
from common.factory.service_factory import get_llm_service, get_gitlab_service, get_code_review_dao
from common.config.code_review_config import CodeReviewConfig
from common.models.code_review_models import CodeReviewRequest, CodeReviewResult, FileChange
from common.utils.token_util import count_tokens, truncate_text_by_tokens
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time
from common.basic.exception import MRFileChangeException, MRFetchException

# 获取日志器
logger = get_logger("code_review")



class CodeReviewService:
    """代码审查服务类，负责业务流程编排"""

    def __init__(self):
        """初始化服务"""
        self.logger = logger
        self.llm_service = get_llm_service()
        self.gitlab_service = get_gitlab_service()
        self.dao = get_code_review_dao()
        self.config = CodeReviewConfig()

    def filter_files(self, file_list: List[str]) -> List[str]:
        """
        过滤文件列表，只保留支持的文件类型

        Args:
            file_list: 文件列表

        Returns:
            List[str]: 过滤后的文件列表
        """
        start_time = datetime.now()
        log_function_call("filter_files", args=(f"file_count:{len(file_list)}",))

        try:
            self.logger.info(f"开始过滤文件列表，总文件数: {len(file_list)}")

            filtered_files = self.gitlab_service.filter_files_by_type(file_list)

            self.logger.info(f"文件过滤完成，保留文件数: {len(filtered_files)}")

            end_time = datetime.now()
            log_execution_time("filter_files", start_time, end_time)

            return filtered_files

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("filter_files", start_time, end_time)
            self.logger.error("文件过滤异常")
            log_exception(e, "过滤文件时发生异常")
            raise

    def filter_mr_changes(self, project_id: str, iid: str) -> List[FileChange]:
        """
        获取并过滤MR变更信息

        Args:
            project_id: GitLab项目ID
            iid: Merge Request ID

        Returns:
            List[FileChange]: 过滤后的变更文件列表
        """
        start_time = datetime.now()
        log_function_call("filter_mr_changes", args=(project_id, iid))

        try:
            self.logger.info(f"开始获取MR变更信息 - 项目ID: {project_id}, MR ID: {iid}")

            # 使用GitLab服务获取变更
            changes_data = self.gitlab_service.get_mr_changes(project_id, iid)

            # 转换为FileChange对象
            file_changes = []
            for change_data in changes_data:
                file_change = FileChange(
                    new_path=change_data["new_path"],
                    diff=change_data["diff"]
                )
                file_changes.append(file_change)

            self.logger.info(f"MR变更获取成功 - 项目ID: {project_id}, MR ID: {iid}, 文件数: {len(file_changes)}")

            end_time = datetime.now()
            log_execution_time("filter_mr_changes", start_time, end_time)

            return file_changes

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("filter_mr_changes", start_time, end_time)
            self.logger.error(f"获取MR变更异常 - 项目ID: {project_id}, MR ID: {iid}")
            log_exception(e, f"获取MR变更时发生错误 - 项目ID: {project_id}, MR ID: {iid}")
            raise


    def review_mr_changes(self, project_id: str, iid: str) -> str:
        """
        评审MR代码变更，调用大模型生成评审意见

        Args:
            project_id: GitLab项目ID
            iid: Merge Request ID

        Returns:
            str: 大模型生成的代码评审意见
        """
        start_time = datetime.now()
        log_function_call("review_mr_changes", args=(project_id, iid))

        try:
            self.logger.info(f"开始MR代码审查 - 项目ID: {project_id}, MR ID: {iid}")

            # 获取MR变更
            file_changes = self.filter_mr_changes(project_id, iid)

            # 格式化变更内容
            changes_str = self.gitlab_service.format_changes_for_review([
                {"new_path": fc.new_path, "diff": fc.diff} for fc in file_changes
            ])

            # 检查token限制并截断
            total_tokens = count_tokens(changes_str)
            max_tokens = self.config.get_max_tokens()

            if total_tokens > max_tokens:
                self.logger.warning(f"变更内容超过最大token限制({max_tokens})，将进行截断")
                changes_str = truncate_text_by_tokens(changes_str, max_tokens)

            # 调用LLM进行审查
            response = self.llm_service.review_code_changes(changes_str)

            self.logger.info(f"MR代码审查完成 - 项目ID: {project_id}, MR ID: {iid}")

            end_time = datetime.now()
            log_execution_time("review_mr_changes", start_time, end_time)

            return response

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("review_mr_changes", start_time, end_time)
            self.logger.error(f"MR代码审查异常 - 项目ID: {project_id}, MR ID: {iid}")
            log_exception(e, f"代码审查过程中发生异常 - 项目ID: {project_id}, MR ID: {iid}")
            raise

    def run_format_task(self, project_id: str, mr_iid: str, llm_output: str, record_id: int):
        """
        后台运行的任务：格式化LLM输出并保存到数据库

        Args:
            project_id: 项目ID
            mr_iid: MR ID
            llm_output: LLM原始输出
            record_id: 数据库记录ID
        """
        start_time = datetime.now()
        log_function_call("run_format_task", args=(project_id, mr_iid, f"llm_output_length:{len(llm_output)}"))

        try:
            self.logger.info(f"开始异步格式化LLM输出 - 项目ID: {project_id}, MR ID: {mr_iid}")

            # 格式化LLM输出
            formatted_result = self.llm_service.format_review_response(llm_output)

            # 保存到数据库
            success = self.dao.update_review_result(record_id, formatted_result)

            if success:
                self.logger.info(f"异步格式化任务完成 - 项目ID: {project_id}, MR ID: {mr_iid}")
            else:
                self.logger.error(f"保存格式化结果失败 - 项目ID: {project_id}, MR ID: {mr_iid}")

            end_time = datetime.now()
            log_execution_time("run_format_task", start_time, end_time)

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("run_format_task", start_time, end_time)
            self.logger.error(f"异步格式化任务异常 - 项目ID: {project_id}, MR ID: {mr_iid}")
            log_exception(e, f"格式化任务过程中发生异常 - 项目ID: {project_id}, MR ID: {mr_iid}")
            # 注意：不要在异步任务中重新抛出异常，这会导致任务静默失败
            # 而是记录异常并继续执行，让任务正常结束



    def format_llm_response(self, llm_output: str) -> Dict[str, Any]:
        """
        格式化LLM输出为结构化数据

        Args:
            llm_output: LLM原始输出

        Returns:
            Dict[str, Any]: 格式化后的结构化数据
        """
        start_time = datetime.now()
        log_function_call("format_llm_response", args=(f"output_length:{len(llm_output)}",))

        try:
            self.logger.info(f"开始格式化LLM输出 - 内容长度: {len(llm_output)}")

            # 使用LLM服务进行格式化
            formatted_result = self.llm_service.format_review_response(llm_output)

            self.logger.info("LLM输出格式化完成")

            end_time = datetime.now()
            log_execution_time("format_llm_response", start_time, end_time)

            return formatted_result

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("format_llm_response", start_time, end_time)
            self.logger.error("LLM输出格式化异常")
            log_exception(e, "格式化LLM输出过程中发生异常")
            raise


# 创建全局服务实例
_code_review_service = None


def get_code_review_service() -> CodeReviewService:
    """获取代码审查服务实例（单例）"""
    global _code_review_service
    if _code_review_service is None:
        _code_review_service = CodeReviewService()
    return _code_review_service


# 为了保持向后兼容，提供原有的函数接口
def filter_files(file_list: List[str]) -> List[str]:
    """过滤文件列表（向后兼容接口）"""
    return get_code_review_service().filter_files(file_list)


def filter_mr_changes(project_id: str, iid: str) -> List[Dict[str, Any]]:
    """获取MR变更（向后兼容接口）"""
    file_changes = get_code_review_service().filter_mr_changes(project_id, iid)
    return [{"new_path": fc.new_path, "diff": fc.diff} for fc in file_changes]


def review_mr_changes(project_id: str, iid: str) -> str:
    """评审MR变更（向后兼容接口）"""
    return get_code_review_service().review_mr_changes(project_id, iid)


def run_format_task(project_id: str, mr_iid: str, llm_output: str, record_id: int):
    """运行格式化任务（向后兼容接口）"""
    return get_code_review_service().run_format_task(project_id, mr_iid, llm_output, record_id)


def format_llm_response(llm_output: str) -> Dict[str, Any]:
    """格式化LLM响应（向后兼容接口）"""
    return get_code_review_service().format_llm_response(llm_output)

if __name__ == "__main__":
        changed_files = [
        "src/main.py",
        "docs/readme.md",
        "config/settings.yaml",
        "data/schema.proto",
        "vendor/lib/dependency.txt",
        "src/module.py",
        "src/module.go",
        "src/module.java",
        "src/module.js",
        "src/module.ts",
        "src/module.pyc",
    ]
        # filtered_files = filter_files(changed_files)
        # print("过滤后的文件列表:") 
        # print(filtered_files)
        # res = review_mr_changes(4142, 306)
        # res = review_mr_changes(2609, 995)
        # res = review_mr_changes(6900,2693)
        # print(f"res:{res}")
        # filter_mr_changes(4142, 306) 
        response = """
        ### 1. 功能整体描述
1. 在BillSettlement实体类中新增taskId字段，用于关联待办任务ID
2. 新增TaskModel类作为任务数据模型，包含任务创建/执行人、标题、参数等字段
3. 在BillSettlementServiceImpl中实现异步任务创建与完成功能：
   - 新增配置参数注入（appKey、secret、API地址等）
   - 新增createTask和completeTask异步方法
   - 实现与4A平台对接的任务创建/完成逻辑
   - 使用OpenClient进行HTTP通信
   - 使用CompletableFuture实现异步处理

---

### 2. 问题识别与优化建议

#### 1. **问题描述**: 异步任务未处理异常
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L497-L499, L512-L514
- **影响**: 异步任务异常未捕获可能导致任务静默失败，影响系统可靠性
- **优化建议**: 添加try-catch并记录异常日志
- **修复优先级**: 高
- **示例代码**:
  ```java
  CompletableFuture.runAsync(() -> {
      try {
          createTaskByOpen(billSettlement, targetUserCode);
      } catch (Exception e) {
          log.error("创建任务异常，结算单ID:{}", billSettlement.getId(), e);
      }
  });
  ```

#### 2. **问题描述**: OpenClient实例未复用
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L522, L541
- **影响**: 频繁创建HTTP客户端可能导致资源泄漏和性能问题
- **优化建议**: 改为单例模式或使用连接池
- **修复优先级**: 中
- **示例代码**:
  ```java
  @Autowired
  private OpenClient openClient; // 通过Spring管理生命周期
  ```

#### 3. **问题描述**: 日志记录包含敏感信息
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L525-L530, L544-L549
- **影响**: 可能泄露API密钥、业务数据等敏感信息
- **优化建议**: 对敏感字段进行脱敏处理
- **修复优先级**: 高
- **示例代码**:
  ```java
  String safeParam = param.toJSONString().replaceAll("(\"appKey\":\")[^\"]+", "$1***");
  String msg = String.format("请求参数：%s, 响应参数：%s", safeParam, json);
  ```

#### 4. **问题描述**: 未验证HTTP响应格式
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L531, L550
- **影响**: 可能因格式错误导致NullPointerException
- **优化建议**: 增加空值校验和格式验证
- **修复优先级**: 中
- **示例代码**:
  ```java
  if (jsonObject != null && jsonObject.containsKey("code")) {
      // 处理逻辑
  }
  ```

#### 5. **问题描述**: TaskModel中priority字段类型不当
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/model/TaskModel.java  
- **行号**: L41
- **影响**: 使用String类型可能导致无效值传入
- **优化建议**: 改用枚举类型或Integer类型
- **修复优先级**: 中
- **示例代码**:
  ```java
  public enum PriorityLevel {
      LOW(0), NORMAL(1), MEDIUM(2), HIGH(3);
      private final int value;
      // 枚举实现
  }
  ```

#### 6. **问题描述**: URL拼接存在注入风险
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L576-L581
- **影响**: 可能导致URL参数注入攻击
- **优化建议**: 使用URI构建器进行参数编码
- **修复优先级**: 高
- **示例代码**:
  ```java
  String actionParameter = UriComponentsBuilder.fromHttpUrl(actionUrl)
      .queryParam("id", billSettlement.getId())
      .queryParam("start", billSettlement.getSettlementPeriodStart())
      .build().toUriString();
  ```

---

### 3. 评分明细

| 评分标准     | 得分 | 说明 |
|--------------|------|------|
| 代码逻辑     | 32/40 | 存在未处理异常、参数校验缺失等问题 |
| 代码风格     | 16/20 | 注释规范但部分命名可优化 |
| 性能优化     | 14/20 | 存在客户端重复创建问题 |
| 安全性       | 6/10  | 存在日志敏感信息泄露风险 |
| 代码可读性   | 8/10  | 部分重复代码可提取公共方法 |

---

### 4. 总分
总分:76分

---

### 5. 建议汇总（按优先级排序）

| 优先级 | 文件路径         | 行号范围   | 问题描述                       | 建议                           |
|--------|------------------|------------|--------------------------------|--------------------------------|
| 高     | BillSettlementServiceImpl.java | L497-L499  | 异步任务未处理异常             | 添加try-catch捕获并记录异常    |
| 高     | BillSettlementServiceImpl.java | L576-L581  | URL拼接存在注入风险            | 使用URI构建器进行参数编码      |
| 高     | BillSettlementServiceImpl.java | L525-L530  | 日志记录包含敏感信息           | 对敏感字段进行脱敏处理         |
| 中     | TaskModel.java   | L41        | priority字段类型不当           | 改用枚举类型或Integer类型      |
| 中     | BillSettlementServiceImpl.java | L531       | 未验证HTTP响应格式             | 增加空值校验和格式验证         |
| 中     | BillSettlementServiceImpl.java | L522       | OpenClient实例未复用           | 改为单例模式或使用连接池       |

---

### 6. 其他建议
1. 建议为TaskModel的param字段添加泛型参数 `<T>` 提高类型安全性
2. 建议将OpenAPI的URL配置合并到配置类中统一管理
3. 建议为CompletableFuture配置自定义线程池提升并发性能
4. 建议在createTaskByOpen方法中添加重试机制
5. 建议将日志记录的msg构造提取为公共方法减少重复代码
        """

        # format_llm_response(response)
        run_format_task(2609, 995, response, 1)